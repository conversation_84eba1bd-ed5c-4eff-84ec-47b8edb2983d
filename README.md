# 闲置市场 - 微信小程序

一个用于发布与浏览闲置物品的简易小程序（本地存储版）。

## 功能
- 浏览列表、搜索、分类筛选
- 发布物品（标题/价格/描述/图片/分类/成色/地点/联系方式）
- 详情页：图片预览、复制联系方式
- 我的发布：查看与删除

## 本地运行
1. 打开微信开发者工具 → 导入项目
2. 选择项目根目录：本仓库根目录
3. AppID 可用 `touristappid`，或替换为你自己的测试号 AppID
4. 预览与调试

## 目录说明
- `project.config.json` 小程序项目配置
- `miniprogram/app.*` 全局入口与样式
- `miniprogram/pages/*` 页面
- `miniprogram/utils/*` 本地存储、初始化与工具

## 说明
- 数据使用 `wx.setStorageSync` 本地存储，无需后端
- 首次打开会自动写入少量示例数据
- 图片选择使用 `wx.chooseMedia`，发布成功后返回上一页即可看到列表更新
