const mongoose = require('mongoose');

const { Schema } = mongoose;

const orderItemSchema = new Schema(
  {
    productId: { type: String, trim: true },
    productName: { type: String, trim: true, required: true },
    skuId: { type: String, trim: true },
    skuName: { type: String, trim: true },
    quantity: { type: Number, required: true, min: 1 },
    priceCents: { type: Number, required: true, min: 0 }
  },
  { _id: false }
);

const addressSchema = new Schema(
  {
    consigneeName: { type: String, trim: true },
    phone: { type: String, trim: true },
    province: { type: String, trim: true },
    city: { type: String, trim: true },
    district: { type: String, trim: true },
    detail: { type: String, trim: true }
  },
  { _id: false }
);

const paymentSchema = new Schema(
  {
    method: { type: String, trim: true },
    transactionId: { type: String, trim: true },
    paidAt: { type: Date }
  },
  { _id: false }
);

const logisticsSchema = new Schema(
  {
    company: { type: String, trim: true },
    trackingNumber: { type: String, trim: true },
    shippedAt: { type: Date },
    deliveredAt: { type: Date }
  },
  { _id: false }
);

const financialsSchema = new Schema(
  {
    totalAmountCents: { type: Number, required: true, min: 0 },
    discountAmountCents: { type: Number, default: 0, min: 0 },
    shippingFeeCents: { type: Number, default: 0, min: 0 },
    payableAmountCents: { type: Number, required: true, min: 0 },
    refundAmountCents: { type: Number, default: 0, min: 0 },
    currency: { type: String, default: 'CNY', trim: true }
  },
  { _id: false }
);

const douyinOrderSchema = new Schema(
  {
    // 内部用户（系统中的 User）
    user: { type: Schema.Types.ObjectId, ref: 'User' },

    // 抖音平台订单信息
    platformOrderId: { type: String, required: true, unique: true, trim: true },
    buyerDouyinOpenId: { type: String, trim: true },
    buyerNickname: { type: String, trim: true },
    shopId: { type: String, trim: true },
    authorId: { type: String, trim: true }, // 主播/达⼈ID
    roomId: { type: String, trim: true },   // 直播间ID
    videoId: { type: String, trim: true },  // 短视频ID
    orderSource: {
      type: String,
      enum: ['live', 'video', 'shop', 'other'],
      default: 'other'
    },

    // 订单明细
    items: { type: [orderItemSchema], default: [] },

    // 收货地址
    address: addressSchema,

    // 状态
    status: {
      type: String,
      enum: [
        'pending',
        'paid',
        'shipped',
        'delivered',
        'completed',
        'cancelled',
        'refunded',
        'partially_refunded'
      ],
      default: 'pending',
      index: true
    },

    // 支付与物流
    payment: paymentSchema,
    logistics: logisticsSchema,

    // 金额
    financials: { type: financialsSchema, required: true },

    // 备注
    remark: { type: String, trim: true }
  },
  { timestamps: true }
);

// 索引
douyinOrderSchema.index({ createdAt: -1 });
douyinOrderSchema.index({ status: 1, createdAt: -1 });
douyinOrderSchema.index({ user: 1, createdAt: -1 });

module.exports = mongoose.model('DouyinOrder', douyinOrderSchema);


