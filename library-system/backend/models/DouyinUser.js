const mongoose = require('mongoose');

const douyinUserSchema = new mongoose.Schema(
  {
    // 基本信息
    douyinId: { 
      type: String, 
      required: true, 
      unique: true, 
      trim: true,
      index: true 
    },
    nickname: { 
      type: String, 
      required: true, 
      trim: true 
    },
    avatar: { 
      type: String, 
      default: '' 
    },
    signature: { 
      type: String, 
      default: '',
      maxlength: 500 
    },
    
    // 账号状态
    isVerified: { 
      type: Boolean, 
      default: false 
    },
    verificationInfo: {
      type: String,
      default: ''
    },
    accountStatus: { 
      type: String, 
      enum: ['active', 'suspended', 'banned', 'inactive'], 
      default: 'active' 
    },
    
    // 统计数据
    followersCount: { 
      type: Number, 
      default: 0,
      min: 0 
    },
    followingCount: { 
      type: Number, 
      default: 0,
      min: 0 
    },
    likesCount: { 
      type: Number, 
      default: 0,
      min: 0 
    },
    videosCount: { 
      type: Number, 
      default: 0,
      min: 0 
    },
    
    // 个人信息
    gender: { 
      type: String, 
      enum: ['male', 'female', 'unknown'], 
      default: 'unknown' 
    },
    age: { 
      type: Number,
      min: 0,
      max: 150 
    },
    location: {
      province: { type: String, default: '' },
      city: { type: String, default: '' },
      district: { type: String, default: '' }
    },
    
    // 联系方式
    email: { 
      type: String, 
      lowercase: true, 
      trim: true,
      validate: {
        validator: function(v) {
          return !v || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
        },
        message: '邮箱格式不正确'
      }
    },
    phone: { 
      type: String,
      validate: {
        validator: function(v) {
          return !v || /^1[3-9]\d{9}$/.test(v);
        },
        message: '手机号格式不正确'
      }
    },
    
    // 内容偏好
    contentCategories: [{
      type: String,
      enum: [
        'entertainment', 'music', 'dance', 'comedy', 'food', 
        'travel', 'fashion', 'beauty', 'sports', 'education',
        'technology', 'gaming', 'pets', 'lifestyle', 'news'
      ]
    }],
    
    // 活跃度数据
    lastActiveTime: { 
      type: Date, 
      default: Date.now 
    },
    registrationDate: { 
      type: Date 
    },
    averageDailyUsage: { 
      type: Number, 
      default: 0,
      min: 0 
    }, // 分钟数
    
    // 商业化信息
    isInfluencer: { 
      type: Boolean, 
      default: false 
    },
    influencerLevel: { 
      type: String, 
      enum: ['none', 'micro', 'macro', 'mega', 'celebrity'], 
      default: 'none' 
    },
    hasLivePermission: { 
      type: Boolean, 
      default: false 
    },
    hasShopPermission: { 
      type: Boolean, 
      default: false 
    },
    
    // 互动数据
    averageLikesPerVideo: { 
      type: Number, 
      default: 0,
      min: 0 
    },
    averageCommentsPerVideo: { 
      type: Number, 
      default: 0,
      min: 0 
    },
    averageSharesPerVideo: { 
      type: Number, 
      default: 0,
      min: 0 
    },
    
    // 设备信息
    deviceInfo: {
      deviceType: { 
        type: String, 
        enum: ['iOS', 'Android', 'unknown'], 
        default: 'unknown' 
      },
      appVersion: { 
        type: String, 
        default: '' 
      }
    },
    
    // 标签和分类
    userTags: [{ 
      type: String, 
      trim: true 
    }],
    
    // 风险评估
    riskLevel: { 
      type: String, 
      enum: ['low', 'medium', 'high'], 
      default: 'low' 
    },
    riskReasons: [{ 
      type: String 
    }],
    
    // 数据更新时间
    dataLastUpdated: { 
      type: Date, 
      default: Date.now 
    },
    
    // 备注信息
    notes: { 
      type: String, 
      maxlength: 1000,
      default: '' 
    }
  },
  { 
    timestamps: true,
    // 添加索引
    indexes: [
      { douyinId: 1 },
      { nickname: 1 },
      { followersCount: -1 },
      { lastActiveTime: -1 },
      { accountStatus: 1 },
      { isInfluencer: 1 }
    ]
  }
);

// 虚拟字段：粉丝等级
douyinUserSchema.virtual('fansLevel').get(function() {
  const followers = this.followersCount;
  if (followers < 1000) return 'beginner';
  if (followers < 10000) return 'growing';
  if (followers < 100000) return 'popular';
  if (followers < 1000000) return 'influential';
  return 'celebrity';
});

// 虚拟字段：活跃度等级
douyinUserSchema.virtual('activityLevel').get(function() {
  const daysSinceLastActive = Math.floor((Date.now() - this.lastActiveTime) / (1000 * 60 * 60 * 24));
  if (daysSinceLastActive <= 1) return 'very_active';
  if (daysSinceLastActive <= 7) return 'active';
  if (daysSinceLastActive <= 30) return 'moderate';
  return 'inactive';
});

// 中间件：更新数据时自动更新dataLastUpdated字段
douyinUserSchema.pre('save', function(next) {
  this.dataLastUpdated = new Date();
  next();
});

// 静态方法：根据粉丝数量查找用户
douyinUserSchema.statics.findByFollowersRange = function(min, max) {
  return this.find({
    followersCount: { $gte: min, $lte: max }
  });
};

// 静态方法：查找活跃用户
douyinUserSchema.statics.findActiveUsers = function(days = 7) {
  const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
  return this.find({
    lastActiveTime: { $gte: cutoffDate },
    accountStatus: 'active'
  });
};

// 实例方法：更新统计数据
douyinUserSchema.methods.updateStats = function(stats) {
  if (stats.followersCount !== undefined) this.followersCount = stats.followersCount;
  if (stats.followingCount !== undefined) this.followingCount = stats.followingCount;
  if (stats.likesCount !== undefined) this.likesCount = stats.likesCount;
  if (stats.videosCount !== undefined) this.videosCount = stats.videosCount;
  
  // 计算平均互动数据
  if (this.videosCount > 0) {
    this.averageLikesPerVideo = Math.round(this.likesCount / this.videosCount);
  }
  
  return this.save();
};

module.exports = mongoose.model('DouyinUser', douyinUserSchema);
