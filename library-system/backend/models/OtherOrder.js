const mongoose = require('mongoose');

const otherOrderSchema = new mongoose.Schema(
  {
    platformOrderId: { type: String, required: true, unique: true, trim: true },
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    source: { type: String, trim: true, default: 'oms' },
    status: {
      type: String,
      enum: [
        'created',
        'paid',
        'processing',
        'shipped',
        'completed',
        'cancelled',
        'refunded'
      ],
      default: 'created',
      index: true
    },
    totalAmountCents: { type: Number, required: true, min: 0 },
    payableAmountCents: { type: Number, required: true, min: 0 }
  },
  { timestamps: true }
);

otherOrderSchema.index({ platformOrderId: 1 }, { unique: true });
otherOrderSchema.index({ user: 1, createdAt: -1 });

module.exports = mongoose.model('OtherOrder', otherOrderSchema);


