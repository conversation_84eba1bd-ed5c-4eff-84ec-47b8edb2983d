{"name": "library-system-backend", "version": "1.0.0", "private": true, "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "seed": "node scripts/seed.js", "seed-douyin": "node scripts/seedDouyinUsers.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.5.2", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.1.0"}}