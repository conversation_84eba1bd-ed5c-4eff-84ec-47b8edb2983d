const express = require('express');
const Borrowing = require('../models/Borrowing');
const Book = require('../models/Book');
const { authenticate, requireRole } = require('../middleware/auth');

const router = express.Router();

router.get('/', authenticate, requireRole(['admin', 'librarian']), async (req, res) => {
  const list = await Borrowing.find().populate('user', 'name email').populate('book', 'title author isbn');
  res.json(list);
});

router.post('/', authenticate, async (req, res) => {
  try {
    const { bookId, days = 14 } = req.body;
    const book = await Book.findById(bookId);
    if (!book || book.availableCopies <= 0) {
      return res.status(400).json({ message: 'Book unavailable' });
    }
    const dueAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
    const record = await Borrowing.create({ user: req.user.id, book: bookId, dueAt });
    book.availableCopies -= 1;
    await book.save();
    res.status(201).json(record);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
});

router.post('/:id/return', authenticate, async (req, res) => {
  try {
    const record = await Borrowing.findById(req.params.id).populate('book');
    if (!record || record.returnedAt) return res.status(400).json({ message: 'Invalid record' });
    record.returnedAt = new Date();
    await record.save();
    record.book.availableCopies += 1;
    await record.book.save();
    res.json(record);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
});

module.exports = router;



