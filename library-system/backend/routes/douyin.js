const express = require('express');
const { authenticate, requireRole } = require('../middleware/auth');
const DouyinOrder = require('../models/DouyinOrder');
const OtherOrder = require('../models/OtherOrder');

const router = express.Router();

// GET /api/douyin/analytics/top-users?limit=10&startDate=2024-01-01&endDate=2024-12-31
router.get('/analytics/top-users', authenticate, requireRole(['admin', 'librarian']), async (req, res) => {
  const { limit = 10, startDate, endDate } = req.query;

  const match = {
    user: { $ne: null },
    status: { $in: ['paid', 'shipped', 'delivered', 'completed', 'partially_refunded'] }
  };

  if (startDate || endDate) {
    match.createdAt = {};
    if (startDate) match.createdAt.$gte = new Date(startDate);
    if (endDate) match.createdAt.$lte = new Date(endDate);
  }

  try {
    const results = await DouyinOrder.aggregate([
      { $match: match },
      {
        $group: {
          _id: '$user',
          totalAmountCents: { $sum: '$financials.payableAmountCents' },
          orderCount: { $sum: 1 }
        }
      },
      { $sort: { totalAmountCents: -1 } },
      { $limit: Number(limit) || 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          _id: 0,
          userId: '$user._id',
          name: '$user.name',
          email: '$user.email',
          totalAmountCents: 1,
          orderCount: 1
        }
      }
    ]);

    res.json(results);
  } catch (err) {
    console.error('Top-users aggregation error:', err);
    res.status(500).json({ message: 'Aggregation failed' });
  }
});

module.exports = router;

// GET /api/douyin/joins/by-platform-id?limit=20
// Left join DouyinOrder -> OtherOrder by platformOrderId
router.get('/joins/by-platform-id', authenticate, requireRole(['admin', 'librarian']), async (req, res) => {
  const { limit = 20 } = req.query;
  try {
    const results = await DouyinOrder.aggregate([
      { $sort: { createdAt: -1 } },
      { $limit: Number(limit) || 20 },
      {
        $lookup: {
          from: 'otherorders',
          localField: 'platformOrderId',
          foreignField: 'platformOrderId',
          as: 'otherOrder'
        }
      },
      { $unwind: { path: '$otherOrder', preserveNullAndEmptyArrays: true } },
      {
        $project: {
          _id: 0,
          platformOrderId: 1,
          douyinStatus: '$status',
          douyinPayableCents: '$financials.payableAmountCents',
          other: {
            status: '$otherOrder.status',
            payableAmountCents: '$otherOrder.payableAmountCents',
            id: '$otherOrder._id'
          }
        }
      }
    ]);
    res.json(results);
  } catch (err) {
    console.error('Join aggregation error:', err);
    res.status(500).json({ message: 'Join aggregation failed' });
  }
});


