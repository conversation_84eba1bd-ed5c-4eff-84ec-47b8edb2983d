const express = require('express');
const DouyinUser = require('../models/DouyinUser');
const router = express.Router();

// 获取所有抖音用户（支持分页和筛选）
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sortBy = 'followersCount',
      sortOrder = 'desc',
      search,
      minFollowers,
      maxFollowers,
      accountStatus,
      isInfluencer,
      contentCategory
    } = req.query;

    // 构建查询条件
    const query = {};
    
    if (search) {
      query.$or = [
        { nickname: { $regex: search, $options: 'i' } },
        { douyinId: { $regex: search, $options: 'i' } },
        { signature: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (minFollowers || maxFollowers) {
      query.followersCount = {};
      if (minFollowers) query.followersCount.$gte = parseInt(minFollowers);
      if (maxFollowers) query.followersCount.$lte = parseInt(maxFollowers);
    }
    
    if (accountStatus) {
      query.accountStatus = accountStatus;
    }
    
    if (isInfluencer !== undefined) {
      query.isInfluencer = isInfluencer === 'true';
    }
    
    if (contentCategory) {
      query.contentCategories = { $in: [contentCategory] };
    }

    // 构建排序条件
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // 执行查询
    const users = await DouyinUser.find(query)
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .select('-__v');

    const total = await DouyinUser.countDocuments(query);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
});

// 根据ID获取单个抖音用户
router.get('/:id', async (req, res) => {
  try {
    const user = await DouyinUser.findById(req.params.id).select('-__v');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      error: error.message
    });
  }
});

// 根据抖音ID获取用户
router.get('/douyin/:douyinId', async (req, res) => {
  try {
    const user = await DouyinUser.findOne({ douyinId: req.params.douyinId }).select('-__v');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      error: error.message
    });
  }
});

// 创建新的抖音用户
router.post('/', async (req, res) => {
  try {
    const user = new DouyinUser(req.body);
    await user.save();

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: user
    });
  } catch (error) {
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: '抖音ID已存在'
      });
    }
    
    res.status(400).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
});

// 更新抖音用户信息
router.put('/:id', async (req, res) => {
  try {
    const user = await DouyinUser.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).select('-__v');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: user
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: '更新用户信息失败',
      error: error.message
    });
  }
});

// 更新用户统计数据
router.patch('/:id/stats', async (req, res) => {
  try {
    const user = await DouyinUser.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    await user.updateStats(req.body);

    res.json({
      success: true,
      message: '统计数据更新成功',
      data: user
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: '更新统计数据失败',
      error: error.message
    });
  }
});

// 删除抖音用户
router.delete('/:id', async (req, res) => {
  try {
    const user = await DouyinUser.findByIdAndDelete(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
});

// 获取用户统计信息
router.get('/analytics/overview', async (req, res) => {
  try {
    const totalUsers = await DouyinUser.countDocuments();
    const verifiedUsers = await DouyinUser.countDocuments({ isVerified: true });
    const influencers = await DouyinUser.countDocuments({ isInfluencer: true });
    const activeUsers = await DouyinUser.countDocuments({
      lastActiveTime: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    });

    // 按粉丝数量分组统计
    const followerStats = await DouyinUser.aggregate([
      {
        $bucket: {
          groupBy: '$followersCount',
          boundaries: [0, 1000, 10000, 100000, 1000000, Infinity],
          default: 'Other',
          output: {
            count: { $sum: 1 },
            avgLikes: { $avg: '$likesCount' }
          }
        }
      }
    ]);

    // 内容分类统计
    const categoryStats = await DouyinUser.aggregate([
      { $unwind: '$contentCategories' },
      { $group: { _id: '$contentCategories', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          verifiedUsers,
          influencers,
          activeUsers
        },
        followerDistribution: followerStats,
        contentCategories: categoryStats
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取统计信息失败',
      error: error.message
    });
  }
});

module.exports = router;
