const express = require('express');
const User = require('../models/User');
const { authenticate, requireRole } = require('../middleware/auth');

const router = express.Router();

router.get('/', authenticate, requireRole(['admin', 'librarian']), async (req, res) => {
  const users = await User.find().select('-password').sort({ createdAt: -1 });
  res.json(users);
});

router.get('/me', authenticate, async (req, res) => {
  const me = await User.findById(req.user.id).select('-password');
  res.json(me);
});

module.exports = router;



