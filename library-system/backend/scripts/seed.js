const mongoose = require('mongoose');
require('dotenv').config({ path: require('path').join(__dirname, '..', '.env') });

const User = require('../models/User');
const Book = require('../models/Book');
const DouyinOrder = require('../models/DouyinOrder');
const OtherOrder = require('../models/OtherOrder');

const MONGO_URI = process.env.MONGO_URI || 'mongodb://127.0.0.1:27017/library_system';

async function seed() {
  await mongoose.connect(MONGO_URI);

  await Promise.all([User.deleteMany({}), Book.deleteMany({}), DouyinOrder.deleteMany({}), OtherOrder.deleteMany({})]);

  const [admin, librarian, member] = await User.create([
    { name: 'Admin', email: '<EMAIL>', password: 'password', role: 'admin' },
    { name: '<PERSON><PERSON><PERSON>', email: '<EMAIL>', password: 'password', role: 'librarian' },
    { name: 'Member', email: '<EMAIL>', password: 'password', role: 'member' }
  ]);

  await Book.create([
    { title: 'Clean Code', author: 'Robert C. Martin', isbn: '9780132350884', category: 'Software', totalCopies: 3, availableCopies: 3 },
    { title: 'The Pragmatic Programmer', author: 'Andrew Hunt', isbn: '9780201616224', category: 'Software', totalCopies: 2, availableCopies: 2 },
    { title: 'Designing Data-Intensive Applications', author: 'Martin Kleppmann', isbn: '9781449373320', category: 'Database', totalCopies: 2, availableCopies: 2 }
  ]);

  // Seed mock Douyin orders
  const users = [admin, librarian, member];
  const now = new Date();
  const orders = [];
  for (let i = 0; i < 60; i++) {
    const u = users[i % users.length];
    const base = 10000 + (i % 7) * 2500; // cents
    orders.push({
      user: u._id,
      platformOrderId: `DY${Date.now()}${i}`,
      buyerDouyinOpenId: `open_${i}`,
      buyerNickname: `buyer_${i}`,
      shopId: 'shop_1',
      authorId: 'author_1',
      roomId: 'room_1',
      videoId: 'video_1',
      orderSource: ['live', 'video', 'shop'][i % 3],
      items: [
        { productId: 'p1', productName: '商品A', skuId: 's1', skuName: '默认', quantity: 1 + (i % 3), priceCents: base }
      ],
      address: { consigneeName: `张三${i}`, phone: '13800000000', province: '北京', city: '北京', district: '朝阳', detail: `望京SOHO ${i}` },
      status: ['paid', 'shipped', 'delivered', 'completed'][i % 4],
      payment: { method: 'wechat', transactionId: `tx_${i}`, paidAt: new Date(now.getTime() - i * 86400000) },
      logistics: { company: '顺丰', trackingNumber: `SF${i}`, shippedAt: new Date(now.getTime() - (i - 1) * 86400000), deliveredAt: new Date(now.getTime() - (i - 2) * 86400000) },
      financials: {
        totalAmountCents: base,
        discountAmountCents: i % 5 === 0 ? 500 : 0,
        shippingFeeCents: 800,
        payableAmountCents: base - (i % 5 === 0 ? 500 : 0) + 800,
        refundAmountCents: 0,
        currency: 'CNY'
      },
      remark: 'seed data',
      createdAt: new Date(now.getTime() - i * 86400000)
    });
  }
  const created = await DouyinOrder.insertMany(orders);

  // Seed OtherOrder to join by platformOrderId (one-to-one mapping for some orders)
  const otherOrders = created.filter((_, idx) => idx % 2 === 0).map((o) => ({
    platformOrderId: o.platformOrderId,
    user: o.user,
    source: 'oms',
    status: o.status === 'completed' ? 'completed' : 'processing',
    totalAmountCents: o.financials.totalAmountCents,
    payableAmountCents: o.financials.payableAmountCents,
    createdAt: o.createdAt,
    updatedAt: o.updatedAt
  }));
  if (otherOrders.length) await OtherOrder.insertMany(otherOrders);

  console.log('✅ Seed completed:', { admin: admin.email, librarian: librarian.email, member: member.email });
  await mongoose.disconnect();
}

seed().catch((e) => {
  console.error(e);
  process.exit(1);
});



