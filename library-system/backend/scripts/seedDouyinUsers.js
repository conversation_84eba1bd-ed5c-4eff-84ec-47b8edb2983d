const mongoose = require('mongoose');
const DouyinUser = require('../models/DouyinUser');
require('dotenv').config();

// 示例抖音用户数据
const sampleDouyinUsers = [
  {
    douyinId: 'dy_001',
    nickname: '小美食家',
    avatar: 'https://example.com/avatar1.jpg',
    signature: '分享美食，分享生活的美好时光 🍜✨',
    isVerified: true,
    verificationInfo: '美食博主',
    followersCount: 125000,
    followingCount: 500,
    likesCount: 2500000,
    videosCount: 320,
    gender: 'female',
    age: 26,
    location: {
      province: '广东省',
      city: '广州市',
      district: '天河区'
    },
    email: '<EMAIL>',
    phone: '13800138001',
    contentCategories: ['food', 'lifestyle'],
    registrationDate: new Date('2022-03-15'),
    isInfluencer: true,
    influencerLevel: 'macro',
    hasLivePermission: true,
    hasShopPermission: true,
    averageLikesPerVideo: 7812,
    averageCommentsPerVideo: 156,
    averageSharesPerVideo: 89,
    deviceInfo: {
      deviceType: 'iOS',
      appVersion: '21.5.0'
    },
    userTags: ['美食', '生活', '分享', '正能量'],
    averageDailyUsage: 180
  },
  {
    douyinId: 'dy_002',
    nickname: '舞蹈小王子',
    avatar: 'https://example.com/avatar2.jpg',
    signature: '用舞蹈诠释青春，用热情点燃梦想 💃🕺',
    isVerified: false,
    followersCount: 68000,
    followingCount: 1200,
    likesCount: 890000,
    videosCount: 156,
    gender: 'male',
    age: 22,
    location: {
      province: '北京市',
      city: '北京市',
      district: '朝阳区'
    },
    phone: '13900139002',
    contentCategories: ['dance', 'music', 'entertainment'],
    registrationDate: new Date('2021-08-20'),
    isInfluencer: true,
    influencerLevel: 'micro',
    hasLivePermission: true,
    hasShopPermission: false,
    averageLikesPerVideo: 5705,
    averageCommentsPerVideo: 234,
    averageSharesPerVideo: 67,
    deviceInfo: {
      deviceType: 'Android',
      appVersion: '21.4.0'
    },
    userTags: ['舞蹈', '音乐', '青春', '活力'],
    averageDailyUsage: 240
  },
  {
    douyinId: 'dy_003',
    nickname: '科技极客',
    avatar: 'https://example.com/avatar3.jpg',
    signature: '探索科技前沿，分享数码生活 📱💻',
    isVerified: true,
    verificationInfo: '科技数码博主',
    followersCount: 89000,
    followingCount: 800,
    likesCount: 1200000,
    videosCount: 245,
    gender: 'male',
    age: 29,
    location: {
      province: '上海市',
      city: '上海市',
      district: '浦东新区'
    },
    email: '<EMAIL>',
    phone: '13700137003',
    contentCategories: ['technology', 'education'],
    registrationDate: new Date('2020-11-10'),
    isInfluencer: true,
    influencerLevel: 'macro',
    hasLivePermission: true,
    hasShopPermission: true,
    averageLikesPerVideo: 4898,
    averageCommentsPerVideo: 189,
    averageSharesPerVideo: 123,
    deviceInfo: {
      deviceType: 'iOS',
      appVersion: '21.5.0'
    },
    userTags: ['科技', '数码', '评测', '教程'],
    averageDailyUsage: 150
  },
  {
    douyinId: 'dy_004',
    nickname: '旅行达人小李',
    avatar: 'https://example.com/avatar4.jpg',
    signature: '世界那么大，我想去看看 🌍✈️',
    isVerified: false,
    followersCount: 45000,
    followingCount: 2000,
    likesCount: 560000,
    videosCount: 189,
    gender: 'female',
    age: 31,
    location: {
      province: '四川省',
      city: '成都市',
      district: '锦江区'
    },
    email: '<EMAIL>',
    contentCategories: ['travel', 'lifestyle'],
    registrationDate: new Date('2021-05-22'),
    isInfluencer: true,
    influencerLevel: 'micro',
    hasLivePermission: false,
    hasShopPermission: false,
    averageLikesPerVideo: 2963,
    averageCommentsPerVideo: 78,
    averageSharesPerVideo: 45,
    deviceInfo: {
      deviceType: 'Android',
      appVersion: '21.3.0'
    },
    userTags: ['旅行', '摄影', '美景', '攻略'],
    averageDailyUsage: 120
  },
  {
    douyinId: 'dy_005',
    nickname: '健身教练阿强',
    avatar: 'https://example.com/avatar5.jpg',
    signature: '健康生活，从运动开始 💪🏃‍♂️',
    isVerified: true,
    verificationInfo: '健身教练',
    followersCount: 156000,
    followingCount: 300,
    likesCount: 3200000,
    videosCount: 412,
    gender: 'male',
    age: 28,
    location: {
      province: '江苏省',
      city: '南京市',
      district: '鼓楼区'
    },
    phone: '13600136005',
    contentCategories: ['sports', 'lifestyle', 'education'],
    registrationDate: new Date('2020-07-08'),
    isInfluencer: true,
    influencerLevel: 'macro',
    hasLivePermission: true,
    hasShopPermission: true,
    averageLikesPerVideo: 7767,
    averageCommentsPerVideo: 298,
    averageSharesPerVideo: 156,
    deviceInfo: {
      deviceType: 'iOS',
      appVersion: '21.5.0'
    },
    userTags: ['健身', '运动', '教学', '励志'],
    averageDailyUsage: 200
  },
  {
    douyinId: 'dy_006',
    nickname: '萌宠小屋',
    avatar: 'https://example.com/avatar6.jpg',
    signature: '记录毛孩子们的日常，传递温暖与爱 🐱🐶',
    isVerified: false,
    followersCount: 78000,
    followingCount: 600,
    likesCount: 1800000,
    videosCount: 298,
    gender: 'female',
    age: 24,
    location: {
      province: '浙江省',
      city: '杭州市',
      district: '西湖区'
    },
    contentCategories: ['pets', 'lifestyle'],
    registrationDate: new Date('2021-12-03'),
    isInfluencer: true,
    influencerLevel: 'micro',
    hasLivePermission: true,
    hasShopPermission: false,
    averageLikesPerVideo: 6040,
    averageCommentsPerVideo: 445,
    averageSharesPerVideo: 89,
    deviceInfo: {
      deviceType: 'Android',
      appVersion: '21.4.0'
    },
    userTags: ['萌宠', '可爱', '治愈', '日常'],
    averageDailyUsage: 160
  }
];

async function seedDouyinUsers() {
  try {
    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/library-system');
    console.log('Connected to MongoDB');

    // 清空现有的抖音用户数据
    await DouyinUser.deleteMany({});
    console.log('Cleared existing Douyin users');

    // 插入示例数据
    const users = await DouyinUser.insertMany(sampleDouyinUsers);
    console.log(`Successfully seeded ${users.length} Douyin users`);

    // 显示插入的用户信息
    users.forEach(user => {
      console.log(`- ${user.nickname} (${user.douyinId}): ${user.followersCount} followers`);
    });

  } catch (error) {
    console.error('Error seeding Douyin users:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedDouyinUsers();
}

module.exports = seedDouyinUsers;
