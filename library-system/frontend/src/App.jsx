import React, { useEffect, useState } from 'react'

function useAuth() {
  const [token, setToken] = useState(localStorage.getItem('token') || '')
  const [user, setUser] = useState(() => {
    const raw = localStorage.getItem('user')
    return raw ? JSON.parse(raw) : null
  })

  const login = async (email, password) => {
    const res = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    })
    if (!res.ok) throw new Error('登录失败')
    const data = await res.json()
    setToken(data.token)
    setUser(data.user)
    localStorage.setItem('token', data.token)
    localStorage.setItem('user', JSON.stringify(data.user))
  }

  const logout = () => {
    setToken('')
    setUser(null)
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  return { token, user, login, logout }
}

function Login({ onLogin }) {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('password')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    try {
      await onLogin(email, password)
    } catch (e) {
      setError(e.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div style={{ maxWidth: 360, margin: '80px auto', fontFamily: 'sans-serif' }}>
      <h2>图书馆管理系统</h2>
      <form onSubmit={handleSubmit}>
        <div>
          <label>邮箱</label>
          <input value={email} onChange={(e) => setEmail(e.target.value)} style={{ width: '100%' }} />
        </div>
        <div>
          <label>密码</label>
          <input type='password' value={password} onChange={(e) => setPassword(e.target.value)} style={{ width: '100%' }} />
        </div>
        {error && <p style={{ color: 'red' }}>{error}</p>}
        <button type='submit' disabled={loading} style={{ width: '100%' }}>
          {loading ? '登录中...' : '登录'}
        </button>
      </form>
      <p style={{ marginTop: 8, color: '#555' }}>示例账户：<EMAIL> / password</p>
    </div>
  )
}

function Dashboard({ token, user, onLogout }) {
  const [q, setQ] = useState('')
  const [books, setBooks] = useState([])
  const [topUsers, setTopUsers] = useState([])
  const [loadingTop, setLoadingTop] = useState(false)
  const [error, setError] = useState('')

  const fetchBooks = async () => {
    setError('')
    const res = await fetch(`/api/books?q=${encodeURIComponent(q)}`, {
      headers: { Authorization: `Bearer ${token}` }
    })
    if (!res.ok) {
      setError('加载失败');
      return
    }
    const data = await res.json()
    setBooks(data)
  }

  const fetchTopUsers = async () => {
    setLoadingTop(true)
    try {
      const res = await fetch(`/api/douyin/analytics/top-users?limit=10`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      if (!res.ok) throw new Error('看板数据加载失败')
      const data = await res.json()
      setTopUsers(data)
    } catch (e) {
      setError(e.message)
    } finally {
      setLoadingTop(false)
    }
  }

  useEffect(() => {
    fetchBooks()
    fetchTopUsers()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div style={{ maxWidth: 1000, margin: '20px auto', fontFamily: 'sans-serif' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3>仪表盘</h3>
        <div>
          <span style={{ marginRight: 12 }}>{user?.name}（{user?.role}）</span>
          <button onClick={onLogout}>退出登录</button>
        </div>
      </div>
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginTop: 12 }}>
        <div>
          <h4>订单额TOP 10 用户</h4>
          {loadingTop ? (
            <p>加载中...</p>
          ) : (
            <table width='100%' cellPadding='8' style={{ borderCollapse: 'collapse' }}>
              <thead>
                <tr>
                  <th align='left'>用户</th>
                  <th align='left'>邮箱</th>
                  <th align='right'>订单数</th>
                  <th align='right'>订单额(¥)</th>
                </tr>
              </thead>
              <tbody>
                {topUsers.map((u) => (
                  <tr key={u.userId}>
                    <td>{u.name}</td>
                    <td>{u.email}</td>
                    <td align='right'>{u.orderCount}</td>
                    <td align='right'>{(u.totalAmountCents / 100).toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
        <div>
          <div style={{ margin: '12px 0', display: 'flex', alignItems: 'center' }}>
            <h4 style={{ margin: 0 }}>图书列表</h4>
            <div style={{ marginLeft: 'auto' }}>
              <input placeholder='搜索标题/作者/ISBN' value={q} onChange={(e) => setQ(e.target.value)} />
              <button onClick={fetchBooks} style={{ marginLeft: 8 }}>搜索</button>
            </div>
          </div>
          {error && <p style={{ color: 'red' }}>{error}</p>}
          <table width='100%' cellPadding='8' style={{ borderCollapse: 'collapse' }}>
            <thead>
              <tr>
                <th align='left'>标题</th>
                <th align='left'>作者</th>
                <th align='left'>ISBN</th>
                <th align='right'>库存</th>
              </tr>
            </thead>
            <tbody>
              {books.map((b) => (
                <tr key={b._id}>
                  <td>{b.title}</td>
                  <td>{b.author}</td>
                  <td>{b.isbn}</td>
                  <td align='right'>{b.availableCopies}/{b.totalCopies}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default function App() {
  const auth = useAuth()
  if (!auth.token) return <Login onLogin={auth.login} />
  return <Dashboard token={auth.token} user={auth.user} onLogout={auth.logout} />
}



