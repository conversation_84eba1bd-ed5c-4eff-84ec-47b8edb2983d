<template>
  <div class="douyin-users">
    <div class="header">
      <h2>抖音用户管理</h2>
      <div class="controls">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索用户昵称或抖音ID..."
          class="search-input"
          @input="handleSearch"
        />
        <select v-model="filterInfluencer" @change="handleFilter" class="filter-select">
          <option value="">全部用户</option>
          <option value="true">网红用户</option>
          <option value="false">普通用户</option>
        </select>
        <select v-model="sortBy" @change="handleSort" class="sort-select">
          <option value="followersCount">按粉丝数排序</option>
          <option value="likesCount">按点赞数排序</option>
          <option value="videosCount">按视频数排序</option>
          <option value="lastActiveTime">按活跃时间排序</option>
        </select>
      </div>
    </div>

    <div class="stats-overview" v-if="analytics">
      <div class="stat-card">
        <h3>总用户数</h3>
        <p class="stat-number">{{ analytics.overview.totalUsers }}</p>
      </div>
      <div class="stat-card">
        <h3>认证用户</h3>
        <p class="stat-number">{{ analytics.overview.verifiedUsers }}</p>
      </div>
      <div class="stat-card">
        <h3>网红用户</h3>
        <p class="stat-number">{{ analytics.overview.influencers }}</p>
      </div>
      <div class="stat-card">
        <h3>活跃用户</h3>
        <p class="stat-number">{{ analytics.overview.activeUsers }}</p>
      </div>
    </div>

    <div class="loading" v-if="loading">
      <p>加载中...</p>
    </div>

    <div class="error" v-if="error">
      <p>{{ error }}</p>
    </div>

    <div class="users-grid" v-if="!loading && users.length > 0">
      <div
        v-for="user in users"
        :key="user._id"
        class="user-card"
        :class="{ verified: user.isVerified, influencer: user.isInfluencer }"
      >
        <div class="user-header">
          <img :src="user.avatar || '/default-avatar.png'" :alt="user.nickname" class="avatar" />
          <div class="user-info">
            <h3 class="nickname">
              {{ user.nickname }}
              <span v-if="user.isVerified" class="verified-badge">✓</span>
              <span v-if="user.isInfluencer" class="influencer-badge">🌟</span>
            </h3>
            <p class="douyin-id">@{{ user.douyinId }}</p>
            <p class="signature">{{ user.signature }}</p>
          </div>
        </div>

        <div class="user-stats">
          <div class="stat">
            <span class="stat-label">粉丝</span>
            <span class="stat-value">{{ formatNumber(user.followersCount) }}</span>
          </div>
          <div class="stat">
            <span class="stat-label">关注</span>
            <span class="stat-value">{{ formatNumber(user.followingCount) }}</span>
          </div>
          <div class="stat">
            <span class="stat-label">点赞</span>
            <span class="stat-value">{{ formatNumber(user.likesCount) }}</span>
          </div>
          <div class="stat">
            <span class="stat-label">视频</span>
            <span class="stat-value">{{ user.videosCount }}</span>
          </div>
        </div>

        <div class="user-details">
          <div class="detail-row">
            <span class="label">性别:</span>
            <span class="value">{{ getGenderText(user.gender) }}</span>
          </div>
          <div class="detail-row" v-if="user.age">
            <span class="label">年龄:</span>
            <span class="value">{{ user.age }}岁</span>
          </div>
          <div class="detail-row" v-if="user.location.city">
            <span class="label">位置:</span>
            <span class="value">{{ user.location.province }} {{ user.location.city }}</span>
          </div>
          <div class="detail-row">
            <span class="label">等级:</span>
            <span class="value">{{ user.influencerLevel }}</span>
          </div>
        </div>

        <div class="user-tags" v-if="user.userTags && user.userTags.length > 0">
          <span v-for="tag in user.userTags" :key="tag" class="tag">{{ tag }}</span>
        </div>

        <div class="user-actions">
          <button @click="viewUserDetail(user)" class="btn btn-primary">查看详情</button>
          <button @click="editUser(user)" class="btn btn-secondary">编辑</button>
        </div>
      </div>
    </div>

    <div class="pagination" v-if="pagination && pagination.pages > 1">
      <button
        @click="changePage(pagination.current - 1)"
        :disabled="pagination.current <= 1"
        class="btn btn-pagination"
      >
        上一页
      </button>
      <span class="page-info">
        第 {{ pagination.current }} 页，共 {{ pagination.pages }} 页
      </span>
      <button
        @click="changePage(pagination.current + 1)"
        :disabled="pagination.current >= pagination.pages"
        class="btn btn-pagination"
      >
        下一页
      </button>
    </div>

    <div class="empty-state" v-if="!loading && users.length === 0">
      <p>暂无用户数据</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DouyinUsers',
  data() {
    return {
      users: [],
      analytics: null,
      loading: false,
      error: null,
      searchQuery: '',
      filterInfluencer: '',
      sortBy: 'followersCount',
      sortOrder: 'desc',
      pagination: null,
      currentPage: 1
    };
  },
  mounted() {
    this.fetchUsers();
    this.fetchAnalytics();
  },
  methods: {
    async fetchUsers() {
      this.loading = true;
      this.error = null;
      
      try {
        const params = new URLSearchParams({
          page: this.currentPage,
          limit: 12,
          sortBy: this.sortBy,
          sortOrder: this.sortOrder
        });

        if (this.searchQuery) {
          params.append('search', this.searchQuery);
        }
        
        if (this.filterInfluencer !== '') {
          params.append('isInfluencer', this.filterInfluencer);
        }

        const response = await fetch(`/api/douyin-users?${params}`);
        const data = await response.json();

        if (data.success) {
          this.users = data.data.users;
          this.pagination = data.data.pagination;
        } else {
          this.error = data.message || '获取用户数据失败';
        }
      } catch (err) {
        this.error = '网络错误，请稍后重试';
        console.error('Error fetching users:', err);
      } finally {
        this.loading = false;
      }
    },

    async fetchAnalytics() {
      try {
        const response = await fetch('/api/douyin-users/analytics/overview');
        const data = await response.json();
        
        if (data.success) {
          this.analytics = data.data;
        }
      } catch (err) {
        console.error('Error fetching analytics:', err);
      }
    },

    handleSearch() {
      this.currentPage = 1;
      this.fetchUsers();
    },

    handleFilter() {
      this.currentPage = 1;
      this.fetchUsers();
    },

    handleSort() {
      this.currentPage = 1;
      this.fetchUsers();
    },

    changePage(page) {
      this.currentPage = page;
      this.fetchUsers();
    },

    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
      }
      return num.toString();
    },

    getGenderText(gender) {
      const genderMap = {
        male: '男',
        female: '女',
        unknown: '未知'
      };
      return genderMap[gender] || '未知';
    },

    viewUserDetail(user) {
      // 实现查看用户详情的逻辑
      console.log('View user detail:', user);
    },

    editUser(user) {
      // 实现编辑用户的逻辑
      console.log('Edit user:', user);
    }
  }
};
</script>

<style scoped>
.douyin-users {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.search-input,
.filter-select,
.sort-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-input {
  min-width: 200px;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.stat-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e9ecef;
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.user-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-card.verified {
  border-left: 4px solid #1da1f2;
}

.user-card.influencer {
  border-left: 4px solid #ff6b6b;
}

.user-header {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.nickname {
  margin: 0 0 5px 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.verified-badge {
  color: #1da1f2;
  font-weight: bold;
}

.influencer-badge {
  color: #ff6b6b;
}

.douyin-id {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.signature {
  margin: 0;
  color: #888;
  font-size: 13px;
  line-height: 1.4;
}

.user-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.user-details {
  margin-bottom: 15px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.label {
  color: #666;
}

.value {
  color: #333;
  font-weight: 500;
}

.user-tags {
  margin-bottom: 15px;
}

.tag {
  display: inline-block;
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-right: 6px;
  margin-bottom: 4px;
}

.user-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 30px;
}

.btn-pagination {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dee2e6;
}

.btn-pagination:hover:not(:disabled) {
  background: #e9ecef;
}

.btn-pagination:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

.loading,
.error,
.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.error {
  color: #dc3545;
}

@media (max-width: 768px) {
  .users-grid {
    grid-template-columns: 1fr;
  }
  
  .header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .controls {
    justify-content: stretch;
  }
  
  .search-input,
  .filter-select,
  .sort-select {
    flex: 1;
    min-width: auto;
  }
}
</style>
