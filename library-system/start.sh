#!/bin/bash

# 图书馆管理系统启动脚本

echo "🚀 启动图书馆管理系统..."

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+"
    exit 1
fi

# 检查 MongoDB 是否运行
if ! pgrep -x "mongod" > /dev/null; then
    echo "⚠️  MongoDB 未运行，请先启动 MongoDB 服务"
    echo "   macOS: brew services start mongodb-community"
    echo "   Ubuntu: sudo systemctl start mongod"
    echo "   Windows: net start MongoDB"
    exit 1
fi

# 进入后端目录
cd backend

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装后端依赖..."
    npm install
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境变量文件..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
    else
        cat > .env <<EOF
MONGO_URI=mongodb://127.0.0.1:27017/library_system
PORT=5000
JWT_SECRET=change_me
EOF
    fi
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
fi

# 初始化数据库（如果需要）
echo "🗄️  初始化数据库..."
npm run seed

# 启动后端服务
echo "🔧 启动后端服务..."
npm run dev &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 进入前端目录
cd ../frontend

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动前端应用
echo "🎨 启动前端应用..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ 图书馆管理系统启动成功！"
echo ""
echo "🌐 前端地址: http://localhost:3000"
echo "🔧 后端地址: http://localhost:5000"
echo ""
echo "📋 测试账户:"
echo "   管理员: <EMAIL> / password"
echo "   图书管理员: <EMAIL> / password"
echo "   会员: <EMAIL> / password"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
trap "echo ''; echo '🛑 正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
