(() => {
  const BOARD_SIZE = 15;
  const LINE_COLOR = '#c8b68c';
  const STAR_COLOR = '#a08b5c';
  const GRID_BG = '#f6f3e9';
  const BLACK_COLOR = '#222';
  const WHITE_COLOR = '#f7f7f7';
  const LAST_MOVE_COLOR = '#d9534f';

  const canvas = document.getElementById('board');
  const ctx = canvas.getContext('2d');
  const statusText = document.getElementById('statusText');
  const restartBtn = document.getElementById('restartBtn');
  const undoBtn = document.getElementById('undoBtn');
  const redoBtn = document.getElementById('redoBtn');

  // State
  let cellSize = 0;
  let boardPadding = 24; // px inside canvas
  let board = createEmptyBoard(); // 0 empty, 1 black, 2 white
  let isBlackTurn = true; // black first
  let winner = 0; // 0 none, 1 black, 2 white, 3 draw
  let moves = []; // history of {r,c,color}
  let redoStack = []; // for redo

  // Resize canvas to device pixel ratio and redraw
  function resizeCanvasToDisplaySize() {
    const ratio = window.devicePixelRatio || 1;
    const displayWidth = canvas.clientWidth;
    const displayHeight = canvas.clientHeight;
    if (canvas.width !== Math.floor(displayWidth * ratio) || canvas.height !== Math.floor(displayHeight * ratio)) {
      canvas.width = Math.floor(displayWidth * ratio);
      canvas.height = Math.floor(displayHeight * ratio);
      ctx.setTransform(ratio, 0, 0, ratio, 0, 0); // scale drawing space
    }
    const size = Math.min(canvas.clientWidth, canvas.clientHeight);
    cellSize = (size - boardPadding * 2) / (BOARD_SIZE - 1);
  }

  function createEmptyBoard() {
    return Array.from({ length: BOARD_SIZE }, () => Array(BOARD_SIZE).fill(0));
  }

  function drawBoard() {
    ctx.save();
    // Clear
    ctx.clearRect(0, 0, canvas.clientWidth, canvas.clientHeight);
    // Background
    ctx.fillStyle = GRID_BG;
    ctx.fillRect(0, 0, canvas.clientWidth, canvas.clientHeight);

    // Grid lines
    ctx.strokeStyle = LINE_COLOR;
    ctx.lineWidth = 1;
    for (let i = 0; i < BOARD_SIZE; i++) {
      const x = boardPadding + i * cellSize;
      const y = boardPadding + i * cellSize;
      // vertical
      ctx.beginPath();
      ctx.moveTo(x, boardPadding);
      ctx.lineTo(x, boardPadding + (BOARD_SIZE - 1) * cellSize);
      ctx.stroke();
      // horizontal
      ctx.beginPath();
      ctx.moveTo(boardPadding, y);
      ctx.lineTo(boardPadding + (BOARD_SIZE - 1) * cellSize, y);
      ctx.stroke();
    }

    // Star points (3-3, 3-11, 11-3, 11-11, 7-7 in 1-based)
    const starCoords = [
      [3, 3], [3, 11], [11, 3], [11, 11], [7, 7]
    ];
    for (const [r1, c1] of starCoords) {
      const [x, y] = toCanvasCoord(r1 - 1, c1 - 1);
      ctx.beginPath();
      ctx.fillStyle = STAR_COLOR;
      ctx.arc(x, y, 3, 0, Math.PI * 2);
      ctx.fill();
    }

    // Stones
    for (let r = 0; r < BOARD_SIZE; r++) {
      for (let c = 0; c < BOARD_SIZE; c++) {
        const val = board[r][c];
        if (val !== 0) {
          drawStone(r, c, val === 1 ? BLACK_COLOR : WHITE_COLOR);
        }
      }
    }

    // Last move indicator
    if (moves.length > 0) {
      const last = moves[moves.length - 1];
      drawLastMoveMarker(last.r, last.c);
    }

    ctx.restore();
  }

  function toCanvasCoord(r, c) {
    const x = boardPadding + c * cellSize;
    const y = boardPadding + r * cellSize;
    return [x, y];
  }

  function drawStone(r, c, color) {
    const [x, y] = toCanvasCoord(r, c);
    const radius = cellSize * 0.45;
    const gradient = ctx.createRadialGradient(x - radius * 0.3, y - radius * 0.3, radius * 0.2, x, y, radius);
    if (color === BLACK_COLOR) {
      gradient.addColorStop(0, '#555');
      gradient.addColorStop(1, BLACK_COLOR);
    } else {
      gradient.addColorStop(0, '#fff');
      gradient.addColorStop(1, '#ddd');
    }
    ctx.beginPath();
    ctx.fillStyle = gradient;
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.strokeStyle = 'rgba(0,0,0,0.15)';
    ctx.stroke();
  }

  function drawLastMoveMarker(r, c) {
    const [x, y] = toCanvasCoord(r, c);
    const half = cellSize * 0.18;
    ctx.save();
    ctx.strokeStyle = LAST_MOVE_COLOR;
    ctx.lineWidth = 2;
    // corner brackets
    // top-left
    ctx.beginPath();
    ctx.moveTo(x - half, y - half + 6);
    ctx.lineTo(x - half, y - half);
    ctx.lineTo(x - half + 6, y - half);
    ctx.stroke();
    // top-right
    ctx.beginPath();
    ctx.moveTo(x + half, y - half + 6);
    ctx.lineTo(x + half, y - half);
    ctx.lineTo(x + half - 6, y - half);
    ctx.stroke();
    // bottom-left
    ctx.beginPath();
    ctx.moveTo(x - half, y + half - 6);
    ctx.lineTo(x - half, y + half);
    ctx.lineTo(x - half + 6, y + half);
    ctx.stroke();
    // bottom-right
    ctx.beginPath();
    ctx.moveTo(x + half, y + half - 6);
    ctx.lineTo(x + half, y + half);
    ctx.lineTo(x + half - 6, y + half);
    ctx.stroke();
    ctx.restore();
  }

  function getCellFromEvent(evt) {
    const rect = canvas.getBoundingClientRect();
    const ratio = (window.devicePixelRatio || 1);
    const x = (evt.clientX - rect.left);
    const y = (evt.clientY - rect.top);
    // Find nearest intersection
    const col = Math.round((x - boardPadding) / cellSize);
    const row = Math.round((y - boardPadding) / cellSize);
    if (row < 0 || row >= BOARD_SIZE || col < 0 || col >= BOARD_SIZE) return null;
    const [cx, cy] = toCanvasCoord(row, col);
    const dist = Math.hypot(cx - x, cy - y);
    if (dist <= cellSize * 0.4) {
      return { r: row, c: col };
    }
    return null;
  }

  function checkWinner(r, c) {
    const color = board[r][c];
    if (!color) return 0;
    const directions = [
      [0, 1], // horizontal
      [1, 0], // vertical
      [1, 1], // diag down-right
      [1, -1], // diag down-left
    ];
    for (const [dr, dc] of directions) {
      let count = 1;
      // extend one way
      let rr = r + dr, cc = c + dc;
      while (inBounds(rr, cc) && board[rr][cc] === color) {
        count++; rr += dr; cc += dc;
      }
      // extend the opposite way
      rr = r - dr; cc = c - dc;
      while (inBounds(rr, cc) && board[rr][cc] === color) {
        count++; rr -= dr; cc -= dc;
      }
      if (count >= 5) return color;
    }
    // Draw?
    const isFull = board.every(row => row.every(v => v !== 0));
    if (isFull) return 3;
    return 0;
  }

  function inBounds(r, c) {
    return r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE;
  }

  function updateStatus() {
    if (winner === 1) {
      statusText.textContent = '黑棋胜利！';
    } else if (winner === 2) {
      statusText.textContent = '白棋胜利！';
    } else if (winner === 3) {
      statusText.textContent = '平局';
    } else {
      statusText.textContent = isBlackTurn ? '黑棋走' : '白棋走';
    }
    undoBtn.disabled = moves.length === 0;
    redoBtn.disabled = redoStack.length === 0;
  }

  function handlePlaceStone(evt) {
    if (winner) return; // game over
    const cell = getCellFromEvent(evt);
    if (!cell) return;
    const { r, c } = cell;
    if (board[r][c] !== 0) return; // occupied
    const color = isBlackTurn ? 1 : 2;
    board[r][c] = color;
    moves.push({ r, c, color });
    redoStack = [];
    winner = checkWinner(r, c);
    if (!winner) isBlackTurn = !isBlackTurn;
    drawBoard();
    updateStatus();
  }

  function restartGame() {
    board = createEmptyBoard();
    isBlackTurn = true;
    winner = 0;
    moves = [];
    redoStack = [];
    drawBoard();
    updateStatus();
  }

  function undo() {
    if (moves.length === 0) return;
    const last = moves.pop();
    board[last.r][last.c] = 0;
    redoStack.push(last);
    winner = 0;
    isBlackTurn = last.color === 1 ? true : false; // revert turn
    drawBoard();
    updateStatus();
  }

  function redo() {
    if (redoStack.length === 0) return;
    const step = redoStack.pop();
    if (board[step.r][step.c] !== 0) return;
    board[step.r][step.c] = step.color;
    moves.push(step);
    winner = checkWinner(step.r, step.c);
    if (!winner) isBlackTurn = step.color === 1 ? false : true;
    drawBoard();
    updateStatus();
  }

  // Events
  canvas.addEventListener('click', handlePlaceStone);
  restartBtn.addEventListener('click', restartGame);
  undoBtn.addEventListener('click', undo);
  redoBtn.addEventListener('click', redo);
  window.addEventListener('resize', () => {
    resizeCanvasToDisplaySize();
    drawBoard();
  });

  // Initial
  function init() {
    // size canvas to element size
    // set initial CSS size via styles.css; here we set internal pixel size for crispness
    resizeCanvasToDisplaySize();
    drawBoard();
    updateStatus();
  }

  // Ensure canvas has a default CSS size in case not laid out yet
  const ensureLayout = () => {
    // force layout to compute clientWidth/Height
    canvas.style.width = '100%';
    canvas.style.height = '100%';
  };

  ensureLayout();
  init();
})();


