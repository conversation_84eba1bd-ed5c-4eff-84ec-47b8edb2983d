# 闲置市场小程序

一个简洁实用的二手闲置物品交易微信小程序，支持物品发布、浏览、搜索和管理功能。

## 功能特性

### 🏠 首页
- 浏览所有发布的闲置物品
- 支持按标题和描述搜索
- 按分类筛选（数码、家居、运动、服饰、其他）
- 点击物品查看详情

### 📝 发布页面
- 发布闲置物品信息
- 支持上传最多6张图片
- 填写标题、价格、分类、成色、地点、联系方式、描述
- 表单验证确保信息完整

### 📱 详情页面
- 查看物品详细信息
- 图片轮播展示
- 一键复制联系方式
- 图片预览功能

### 👤 我的页面
- 查看自己发布的所有物品
- 删除已发布的物品
- 管理个人闲置物品

## 技术特点

- **本地存储**：使用微信小程序本地存储API保存数据
- **响应式设计**：适配不同屏幕尺寸
- **现代UI**：简洁美观的界面设计
- **用户体验**：流畅的交互和反馈

## 项目结构

```
miniprogram/
├── app.js              # 小程序入口文件
├── app.json            # 小程序配置文件
├── app.wxss            # 全局样式文件
├── pages/              # 页面文件夹
│   ├── index/          # 首页
│   ├── publish/        # 发布页面
│   ├── detail/         # 详情页面
│   └── my/             # 我的页面
├── utils/              # 工具函数
│   ├── storage.js      # 数据存储工具
│   ├── uuid.js         # UUID生成工具
│   └── seed.js         # 示例数据
├── images/             # 图标资源
└── sitemap.json        # 站点地图
```

## 使用说明

1. **开发环境**：使用微信开发者工具打开项目
2. **预览**：点击预览按钮生成二维码，用微信扫码体验
3. **发布**：配置小程序AppID后可发布到微信小程序平台

## 自定义配置

### 修改分类
在 `pages/index/index.js` 和 `pages/publish/index.js` 中修改 `categories` 数组。

### 修改成色选项
在 `pages/publish/index.js` 中修改 `conditions` 数组。

### 添加图标
在 `images/` 文件夹中添加底部导航栏所需的图标文件：
- home.png / home-active.png
- publish.png / publish-active.png  
- my.png / my-active.png

### 样式定制
修改 `app.wxss` 中的全局样式变量，或各页面的 `.wxss` 文件。

## 注意事项

- 图片上传功能需要在真机上测试
- 数据存储在本地，卸载小程序会丢失数据
- 如需云端存储，可集成微信云开发或其他后端服务

## 扩展功能建议

- 用户登录和身份认证
- 云端数据存储和同步
- 消息通知功能
- 地理位置服务
- 在线支付功能
- 评价和信用系统

## 开发者

这是一个开源的微信小程序项目，欢迎贡献代码和提出建议。
