page {
  background-color: #f7f8fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.container {
  padding: 12px;
  padding-bottom: 20px;
}

.card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid rgba(0,0,0,0.04);
}

.color-primary {
  color: #1677ff;
}

.btn-primary {
  background: #1677ff;
  color: #ffffff;
  border-radius: 8px;
  border: none;
  font-weight: 500;
}

.btn-primary:hover {
  background: #0958d9;
}

/* 通用输入框样式 */
input, textarea {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
  color: #333;
}

input:focus, textarea:focus {
  border-color: #1677ff;
  outline: none;
}

/* 通用按钮样式 */
button {
  border-radius: 8px;
  font-weight: 500;
}

button[size="mini"] {
  padding: 6px 12px;
  font-size: 12px;
}

/* 文本样式 */
.text-primary {
  color: #333;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

/* 间距工具类 */
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.mt-5 { margin-top: 20px; }

.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-5 { margin-bottom: 20px; }

.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }
