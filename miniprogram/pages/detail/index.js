const { getItemById } = require('../../utils/storage.js');

Page({
  data: { item: null },
  onLoad(query) {
    const id = query.id;
    if (!id) return;
    const item = getItemById(id);
    this.setData({ item });
  },
  preview() {
    const item = this.data.item;
    if (!item || !item.images || !item.images.length) return;
    wx.previewImage({ current: item.images[0], urls: item.images });
  },
  copy() {
    const item = this.data.item;
    if (!item) return;
    wx.setClipboardData({ data: item.contact, success: () => wx.showToast({ title: '已复制' }) });
  }
});
