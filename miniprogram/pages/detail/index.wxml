<view class="container">
  <block wx:if="{{item}}">
    <view class="gallery">
      <swiper indicator-dots autoplay interval="3000" circular wx:if="{{item.images && item.images.length}}">
        <block wx:for="{{item.images}}" wx:key="*this">
          <swiper-item>
            <image class="hero" mode="aspectFill" src="{{item}}" bindtap="preview"></image>
          </swiper-item>
        </block>
      </swiper>
      <view class="hero placeholder" wx:else></view>
    </view>
    <view class="card section">
      <view class="title">{{item.title}}</view>
      <view class="price color-primary">¥{{item.price}}</view>
      <view class="sub">{{item.category}} · {{item.condition}} · {{item.location}}</view>
    </view>
    <view class="card section">
      <view class="desc">{{item.desc || '暂无描述'}}</view>
    </view>
    <view class="card section">
      <view class="contact">联系方式：{{item.contact}}</view>
      <button size="mini" class="btn-primary" bindtap="copy">复制联系方式</button>
    </view>
  </block>
  <block wx:else>
    <view class="empty">未找到该物品</view>
  </block>
</view>
