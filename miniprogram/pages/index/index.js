const { loadItems } = require('../../utils/storage.js');

Page({
  data: {
    items: [],
    filtered: [],
    search: '',
    categories: ['全部', '数码', '家居', '运动', '服饰', '其他'],
    categoryIndex: 0
  },
  onShow() {
    this.refresh();
  },
  refresh() {
    const items = loadItems();
    this.setData({ items }, () => this.applyFilter());
  },
  onSearchInput(e) {
    this.setData({ search: e.detail.value }, () => this.applyFilter());
  },
  onSearchConfirm() {
    this.applyFilter();
  },
  onCategoryChange(e) {
    this.setData({ categoryIndex: Number(e.detail.value) }, () => this.applyFilter());
  },
  applyFilter() {
    const { items, search, categoryIndex, categories } = this.data;
    const kw = (search || '').trim().toLowerCase();
    const cat = categories[categoryIndex];
    const list = items.filter((it) => {
      const hitKw = !kw || (it.title + ' ' + (it.desc || '')).toLowerCase().includes(kw);
      const hitCat = cat === '全部' || it.category === cat;
      return hitKw && hitCat;
    });
    this.setData({ filtered: list });
  },
  goDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({ url: `/pages/detail/index?id=${id}` });
  }
});
