<view class="container">
  <view class="search-bar card">
    <input class="search-input" placeholder="搜索标题或描述" value="{{search}}" bindinput="onSearchInput" confirm-type="search" bindconfirm="onSearchConfirm" />
    <picker mode="selector" range="{{categories}}" value="{{categoryIndex}}" bindchange="onCategoryChange">
      <view class="category-picker">{{categories[categoryIndex]}}</view>
    </picker>
  </view>

  <block wx:if="{{filtered.length > 0}}">
    <view class="list">
      <block wx:for="{{filtered}}" wx:key="id">
        <view class="card item" data-id="{{item.id}}" bindtap="goDetail">
          <block wx:if="{{item.images && item.images.length}}">
            <image class="thumb" mode="aspectFill" src="{{item.images[0]}}"></image>
          </block>
          <view wx:else class="thumb thumb-ph"></view>
          <view class="meta">
            <view class="title">{{item.title}}</view>
            <view class="price color-primary">¥{{item.price}}</view>
            <view class="sub">{{item.category}} · {{item.condition}} · {{item.location}}</view>
          </view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <view class="empty">暂无数据，点击底部"发布"按钮开始发布闲置物品</view>
  </block>
</view>
