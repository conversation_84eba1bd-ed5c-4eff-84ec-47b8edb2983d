.search-bar { padding: 10px; display: flex; gap: 10px; align-items: center; }
.search-input { flex: 1; background: #f2f3f5; padding: 8px 10px; border-radius: 8px; }
.category-picker { padding: 8px 10px; background: #f2f3f5; border-radius: 8px; }

.list { margin-top: 12px; display: flex; flex-direction: column; gap: 12px; }
.item { display: flex; gap: 12px; padding: 10px; }
.thumb { width: 88px; height: 88px; background: #f2f3f5; border-radius: 8px; }
.thumb-ph { display: block; background: linear-gradient(135deg,#f2f3f5,#e5e6eb); }
.meta { flex: 1; display: flex; flex-direction: column; gap: 4px; }
.title { font-weight: 600; font-size: 16px; color: #1f2329; }
.price { font-size: 16px; }
.sub { color: #86909c; font-size: 12px; }

.empty {
  margin: 60px 0;
  text-align: center;
  color: #999;
  font-size: 14px;
  line-height: 1.6;
}
