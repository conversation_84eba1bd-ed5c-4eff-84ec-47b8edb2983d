const { loadItems, deleteItemById } = require('../../utils/storage.js');

Page({
  data: { mine: [] },
  onShow() { this.refresh(); },
  refresh() {
    const all = loadItems();
    const mine = all.filter((it) => it.ownerIsMe);
    this.setData({ mine });
  },
  remove(e) {
    const id = e.currentTarget.dataset.id;
    const that = this;
    wx.showModal({
      title: '确认删除',
      content: '删除后不可恢复，是否继续？',
      success(res) {
        if (res.confirm) {
          deleteItemById(id);
          wx.showToast({ title: '已删除' });
          that.refresh();
        }
      }
    });
  }
});
