.list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item {
  display: flex;
  gap: 16px;
  padding: 16px;
  align-items: center;
}

.thumb {
  width: 80px;
  height: 80px;
  background: #f2f3f5;
  border-radius: 8px;
  flex-shrink: 0;
}

.meta {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  line-height: 1.4;
}

.price {
  font-size: 16px;
  font-weight: 600;
}

.sub {
  color: #666;
  font-size: 12px;
}

.empty {
  text-align: center;
  color: #999;
  padding: 60px 20px;
  font-size: 14px;
}
