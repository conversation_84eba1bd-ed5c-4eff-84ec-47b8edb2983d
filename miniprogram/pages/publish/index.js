const { addItem } = require('../../utils/storage.js');
const { uuid } = require('../../utils/uuid.js');

Page({
  data: {
    categories: ['数码', '家居', '运动', '服饰', '其他'],
    conditions: ['全新', '九五新', '九成新', '八成新', '可用'],
    categoryIndex: 0,
    conditionIndex: 2,
    form: {
      title: '',
      price: '',
      desc: '',
      images: [],
      category: '数码',
      condition: '九成新',
      location: '',
      contact: ''
    }
  },
  onInput(e) {
    const key = e.currentTarget.dataset.key;
    const val = e.detail.value;
    this.setData({ [`form.${key}`]: val });
  },
  onCategoryChange(e) {
    const idx = Number(e.detail.value);
    this.setData({ categoryIndex: idx, 'form.category': this.data.categories[idx] });
  },
  onConditionChange(e) {
    const idx = Number(e.detail.value);
    this.setData({ conditionIndex: idx, 'form.condition': this.data.conditions[idx] });
  },
  chooseImages() {
    const that = this;
    wx.chooseMedia({
      count: 6,
      mediaType: ['image'],
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        const files = (res.tempFiles || []).map(f => f.tempFilePath);
        const next = (that.data.form.images || []).concat(files).slice(0, 6);
        that.setData({ 'form.images': next });
      }
    });
  },
  previewImage(e) {
    const idx = Number(e.currentTarget.dataset.idx);
    wx.previewImage({
      current: this.data.form.images[idx],
      urls: this.data.form.images
    });
  },
  submit() {
    const f = this.data.form;
    if (!f.title.trim()) return wx.showToast({ title: '请输入标题', icon: 'none' });
    const priceNum = Number(f.price);
    if (!priceNum || priceNum < 0) return wx.showToast({ title: '价格不正确', icon: 'none' });
    if (!f.contact.trim()) return wx.showToast({ title: '请输入联系方式', icon: 'none' });

    const item = {
      id: uuid(),
      title: f.title.trim(),
      price: priceNum,
      desc: f.desc.trim(),
      images: f.images,
      category: f.category,
      condition: f.condition,
      location: f.location.trim(),
      contact: f.contact.trim(),
      ownerIsMe: true,
      createdAt: Date.now()
    };
    addItem(item);
    wx.showToast({ title: '发布成功' });
    setTimeout(() => {
      wx.navigateBack();
    }, 400);
  }
});
