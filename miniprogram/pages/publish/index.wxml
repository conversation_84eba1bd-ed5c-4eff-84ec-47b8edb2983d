<view class="container">
  <view class="card form">
    <view class="field">
      <text class="label">标题</text>
      <input placeholder="如：Kindle 8代" value="{{form.title}}" bindinput="onInput" data-key="title" />
    </view>

    <view class="field">
      <text class="label">价格（¥）</text>
      <input type="number" placeholder="如：199" value="{{form.price}}" bindinput="onInput" data-key="price" />
    </view>

    <view class="field">
      <text class="label">分类</text>
      <picker mode="selector" range="{{categories}}" value="{{categoryIndex}}" bindchange="onCategoryChange">
        <view class="picker">{{categories[categoryIndex]}}</view>
      </picker>
    </view>

    <view class="field">
      <text class="label">成色</text>
      <picker mode="selector" range="{{conditions}}" value="{{conditionIndex}}" bindchange="onConditionChange">
        <view class="picker">{{conditions[conditionIndex]}}</view>
      </picker>
    </view>

    <view class="field">
      <text class="label">地点</text>
      <input placeholder="如：朝阳" value="{{form.location}}" bindinput="onInput" data-key="location" />
    </view>

    <view class="field">
      <text class="label">联系方式</text>
      <input placeholder="微信号/手机号" value="{{form.contact}}" bindinput="onInput" data-key="contact" />
    </view>

    <view class="field">
      <text class="label">描述</text>
      <textarea placeholder="补充说明" value="{{form.desc}}" bindinput="onInput" data-key="desc" auto-height />
    </view>

    <view class="field">
      <text class="label">图片</text>
      <view class="images">
        <block wx:for="{{form.images}}" wx:key="*this">
          <image class="img" src="{{item}}" mode="aspectFill" data-idx="{{index}}" bindtap="previewImage" />
        </block>
        <button class="btn-primary add" size="mini" bindtap="chooseImages">添加图片</button>
      </view>
    </view>

    <button class="btn-primary submit" bindtap="submit">发布</button>
  </view>
</view>
