.form {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  color: #333;
  font-size: 15px;
  font-weight: 500;
}

.field input, .field textarea {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
}

.field input:focus, .field textarea:focus {
  border-color: #1677ff;
}

.picker {
  background: #fff;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  color: #333;
}

.images {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.img {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  background: #f2f3f5;
  position: relative;
}

.add {
  margin-left: 0;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border: 2px dashed #d9d9d9;
  background: transparent;
  color: #666;
}

.submit {
  margin-top: 20px;
  padding: 15px;
  font-size: 16px;
  font-weight: 500;
}
