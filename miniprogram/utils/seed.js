const { loadItems, saveItems } = require('./storage.js');
const { uuid } = require('./uuid.js');

function seedIfEmpty() {
  const existing = loadItems();
  if (existing && existing.length > 0) return;
  const now = Date.now();
  const samples = [
    {
      id: uuid(),
      title: 'iPad 2018 32G',
      price: 900,
      desc: '九成新，电池耐用，带数据线。',
      images: [],
      category: '数码',
      condition: '九成新',
      location: '朝阳',
      contact: 'wxid-demo-001',
      ownerIsMe: false,
      createdAt: now
    },
    {
      id: uuid(),
      title: '宜家书桌 120cm',
      price: 160,
      desc: '自提，轻微划痕。',
      images: [],
      category: '家居',
      condition: '八成新',
      location: '海淀',
      contact: 'wxid-demo-002',
      ownerIsMe: false,
      createdAt: now - 10000
    },
    {
      id: uuid(),
      title: '跑步机 几乎全新',
      price: 800,
      desc: '闲置低价转，有发票。',
      images: [],
      category: '运动',
      condition: '九五新',
      location: '昌平',
      contact: 'wxid-demo-003',
      ownerIsMe: false,
      createdAt: now - 20000
    }
  ];
  saveItems(samples);
}

module.exports = { seedIfEmpty };
