const STORAGE_KEY = 'idle_items';

function loadItems() {
  try {
    const items = wx.getStorageSync(STORAGE_KEY);
    return Array.isArray(items) ? items : [];
  } catch (error) {
    console.error('Failed to load items from storage', error);
    return [];
  }
}

function saveItems(items) {
  try {
    wx.setStorageSync(STORAGE_KEY, items || []);
  } catch (error) {
    console.error('Failed to save items to storage', error);
  }
}

function addItem(item) {
  const items = loadItems();
  items.unshift(item);
  saveItems(items);
  return item;
}

function updateItemById(itemId, patch) {
  const items = loadItems();
  const updated = items.map((it) => (it.id === itemId ? { ...it, ...patch } : it));
  saveItems(updated);
}

function deleteItemById(itemId) {
  const items = loadItems();
  const filtered = items.filter((it) => it.id !== itemId);
  saveItems(filtered);
}

function getItemById(itemId) {
  const items = loadItems();
  return items.find((it) => it.id === itemId);
}

module.exports = {
  STORAGE_KEY,
  loadItems,
  saveItems,
  addItem,
  updateItemById,
  deleteItemById,
  getItemById
};
