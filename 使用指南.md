# 闲置市场小程序使用指南

## 🚀 快速开始

### 1. 环境准备
- 下载并安装[微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- 注册微信小程序账号（可选，用于发布）

### 2. 导入项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目目录（包含 `miniprogram` 文件夹的目录）
4. 填写项目名称，AppID可以使用测试号
5. 点击"导入"

### 3. 预览体验
- 点击工具栏的"预览"按钮
- 用微信扫描生成的二维码
- 在手机上体验小程序功能

## 📱 功能使用

### 首页浏览
- **搜索**：在搜索框输入关键词查找物品
- **筛选**：点击分类选择器按分类浏览
- **查看详情**：点击任意物品卡片进入详情页

### 发布物品
1. 点击底部"发布"标签
2. 填写物品信息：
   - 标题：简洁描述物品
   - 价格：输入数字金额
   - 分类：选择合适的分类
   - 成色：选择物品新旧程度
   - 地点：填写所在区域
   - 联系方式：微信号或手机号
   - 描述：详细说明（可选）
3. 添加图片：点击"添加图片"按钮上传照片
4. 点击"发布"完成

### 管理物品
1. 点击底部"我的"标签
2. 查看已发布的所有物品
3. 点击"删除"按钮可删除不需要的物品

## 🎨 自定义配置

### 修改分类选项
编辑以下文件中的 `categories` 数组：
- `miniprogram/pages/index/index.js`
- `miniprogram/pages/publish/index.js`

```javascript
categories: ['数码', '家居', '运动', '服饰', '图书', '其他']
```

### 修改成色选项
编辑 `miniprogram/pages/publish/index.js` 中的 `conditions` 数组：

```javascript
conditions: ['全新', '九五新', '九成新', '八成新', '七成新', '可用']
```

### 更换主题色
编辑 `miniprogram/app.wxss` 中的颜色变量：

```css
.color-primary { color: #1677ff; }  /* 主色调 */
.btn-primary { background: #1677ff; }  /* 按钮颜色 */
```

### 添加底部导航图标
在 `miniprogram/images/` 文件夹中添加以下图标文件：
- `home.png` 和 `home-active.png` (首页图标)
- `publish.png` 和 `publish-active.png` (发布图标)
- `my.png` 和 `my-active.png` (我的图标)

推荐尺寸：81px × 81px，PNG格式，透明背景

## 🔧 开发说明

### 项目结构
```
miniprogram/
├── pages/          # 页面文件
├── utils/          # 工具函数
├── images/         # 图标资源
├── app.js          # 小程序入口
├── app.json        # 配置文件
└── app.wxss        # 全局样式
```

### 数据存储
- 使用微信小程序本地存储API
- 数据保存在用户设备上
- 卸载小程序会清除数据

### 扩展开发
如需添加新功能，可以：
1. 在 `pages/` 目录创建新页面
2. 在 `app.json` 中注册页面路径
3. 在 `utils/` 中添加工具函数

## 📝 发布上线

### 1. 申请小程序
1. 访问[微信公众平台](https://mp.weixin.qq.com/)
2. 注册小程序账号
3. 获取AppID

### 2. 配置项目
1. 在 `project.config.json` 中填入真实的AppID
2. 配置服务器域名（如有后端服务）
3. 完善小程序信息

### 3. 提交审核
1. 点击"上传"按钮上传代码
2. 在微信公众平台提交审核
3. 审核通过后发布上线

## ❓ 常见问题

**Q: 图片上传不显示？**
A: 图片功能需要在真机上测试，模拟器可能不支持。

**Q: 数据丢失了？**
A: 数据存储在本地，清除小程序数据或卸载会导致数据丢失。

**Q: 如何添加更多功能？**
A: 可以集成微信云开发或搭建后端服务来扩展功能。

**Q: 样式显示异常？**
A: 检查CSS语法，确保样式文件路径正确。

## 📞 技术支持

如遇到问题或需要帮助，可以：
1. 查看微信小程序官方文档
2. 在开发者社区提问
3. 检查控制台错误信息

祝您使用愉快！🎉
