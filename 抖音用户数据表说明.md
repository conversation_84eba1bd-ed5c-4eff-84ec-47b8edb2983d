# 抖音用户数据表系统

## 概述

这是一个完整的抖音用户数据管理系统，包含数据模型、API接口、前端展示和数据初始化功能。系统基于Node.js + MongoDB + Vue.js技术栈构建。

## 功能特性

### 数据模型特性
- **完整的用户信息**：包含基本信息、统计数据、个人信息、联系方式等
- **智能分类**：支持内容分类、用户标签、影响力等级等
- **数据验证**：内置邮箱、手机号格式验证
- **索引优化**：针对常用查询字段建立索引
- **虚拟字段**：自动计算粉丝等级、活跃度等级

### API功能
- **CRUD操作**：完整的增删改查功能
- **高级查询**：支持搜索、筛选、排序、分页
- **统计分析**：提供用户概览、分布统计等
- **批量操作**：支持批量更新统计数据

### 前端界面
- **响应式设计**：适配桌面和移动端
- **实时搜索**：支持昵称、抖音ID搜索
- **多维筛选**：按用户类型、粉丝数量等筛选
- **数据可视化**：统计卡片、用户卡片展示

## 文件结构

```
library-system/
├── backend/
│   ├── models/
│   │   └── DouyinUser.js          # 抖音用户数据模型
│   ├── routes/
│   │   └── douyinUsers.js         # API路由
│   ├── scripts/
│   │   └── seedDouyinUsers.js     # 数据初始化脚本
│   ├── server.js                  # 服务器配置（已更新）
│   └── package.json               # 依赖配置（已更新）
└── frontend/
    └── src/
        └── components/
            └── DouyinUsers.vue    # 前端组件
```

## 数据模型字段说明

### 基本信息
- `douyinId`: 抖音ID（唯一标识）
- `nickname`: 用户昵称
- `avatar`: 头像URL
- `signature`: 个性签名

### 账号状态
- `isVerified`: 是否认证
- `verificationInfo`: 认证信息
- `accountStatus`: 账号状态（active/suspended/banned/inactive）

### 统计数据
- `followersCount`: 粉丝数
- `followingCount`: 关注数
- `likesCount`: 获赞数
- `videosCount`: 视频数

### 个人信息
- `gender`: 性别
- `age`: 年龄
- `location`: 地理位置（省市区）
- `email`: 邮箱
- `phone`: 手机号

### 内容偏好
- `contentCategories`: 内容分类数组
- `userTags`: 用户标签

### 活跃度数据
- `lastActiveTime`: 最后活跃时间
- `registrationDate`: 注册时间
- `averageDailyUsage`: 日均使用时长（分钟）

### 商业化信息
- `isInfluencer`: 是否为网红
- `influencerLevel`: 影响力等级
- `hasLivePermission`: 是否有直播权限
- `hasShopPermission`: 是否有商店权限

### 互动数据
- `averageLikesPerVideo`: 平均每视频点赞数
- `averageCommentsPerVideo`: 平均每视频评论数
- `averageSharesPerVideo`: 平均每视频分享数

## 安装和使用

### 1. 安装依赖
```bash
cd library-system/backend
npm install
```

### 2. 配置环境变量
创建 `.env` 文件：
```env
MONGODB_URI=mongodb://localhost:27017/library-system
PORT=5000
```

### 3. 初始化数据
```bash
# 运行抖音用户数据初始化
npm run seed-douyin
```

### 4. 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 5. 前端集成
将 `DouyinUsers.vue` 组件集成到您的Vue.js应用中。

## API接口说明

### 基础路径
所有API接口的基础路径为：`/api/douyin-users`

### 主要接口

#### 1. 获取用户列表
```
GET /api/douyin-users
```

查询参数：
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）
- `search`: 搜索关键词
- `minFollowers`: 最小粉丝数
- `maxFollowers`: 最大粉丝数
- `accountStatus`: 账号状态
- `isInfluencer`: 是否为网红
- `contentCategory`: 内容分类
- `sortBy`: 排序字段（默认followersCount）
- `sortOrder`: 排序方向（desc/asc）

#### 2. 获取单个用户
```
GET /api/douyin-users/:id
GET /api/douyin-users/douyin/:douyinId
```

#### 3. 创建用户
```
POST /api/douyin-users
```

#### 4. 更新用户
```
PUT /api/douyin-users/:id
PATCH /api/douyin-users/:id/stats
```

#### 5. 删除用户
```
DELETE /api/douyin-users/:id
```

#### 6. 获取统计信息
```
GET /api/douyin-users/analytics/overview
```

## 示例数据

系统包含6个示例用户：
1. **小美食家** - 美食博主，12.5万粉丝
2. **舞蹈小王子** - 舞蹈达人，6.8万粉丝
3. **科技极客** - 科技博主，8.9万粉丝
4. **旅行达人小李** - 旅行博主，4.5万粉丝
5. **健身教练阿强** - 健身教练，15.6万粉丝
6. **萌宠小屋** - 宠物博主，7.8万粉丝

## 扩展功能

### 虚拟字段
- `fansLevel`: 根据粉丝数自动计算等级
- `activityLevel`: 根据最后活跃时间计算活跃度

### 静态方法
- `findByFollowersRange(min, max)`: 按粉丝数范围查找
- `findActiveUsers(days)`: 查找活跃用户

### 实例方法
- `updateStats(stats)`: 更新统计数据

## 注意事项

1. **数据验证**：邮箱和手机号有格式验证
2. **索引优化**：已为常用查询字段建立索引
3. **数据一致性**：更新时自动更新`dataLastUpdated`字段
4. **错误处理**：API包含完整的错误处理机制
5. **安全性**：建议在生产环境中添加认证和授权机制

## 技术栈

- **后端**: Node.js + Express + MongoDB + Mongoose
- **前端**: Vue.js + CSS3
- **数据库**: MongoDB
- **工具**: bcryptjs, cors, dotenv, morgan

## 许可证

本项目仅供学习和参考使用。
